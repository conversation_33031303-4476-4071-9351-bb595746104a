# Development Guide - Internal Documentation
development-guide.md

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary folders
tmp/
temp/

# Build outputs
dist/
build/

# Cache files
.cache/
.parcel-cache/

# Local development files
*.local

# Backup files
*.bak
*.backup
*.old

# Test files (if any)
test/
tests/
__tests__/

# Documentation drafts
docs/drafts/
*.draft.md

# Personal notes
notes.txt
todo.txt
*.notes

# IDE specific files
*.sublime-project
*.sublime-workspace

# Windows specific
desktop.ini

# Mac specific
.AppleDouble
.LSOverride

# Linux specific
*~

# Temporary files
*.tmp
*.temp
