# 🪑 متجر الأثاث الفاخر - Furniture Store

مشروع موقع ويب لمتجر أثاث باستخدام HTML, CSS, JavaScript و Tailwind CSS

## 📋 وصف المشروع

موقع ويب متجاوب ومتكامل لمتجر أثاث يتضمن:
- صفحة رئيسية جذابة
- عرض المنتجات
- معلومات عن المتجر
- نموذج اتصال
- دعم كامل للغة العربية (RTL)

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحة
- **CSS3** - التنسيق والتصميم
- **JavaScript (Vanilla)** - التفاعلات والوظائف
- **Tailwind CSS** - إط<PERSON>ر عمل CSS (عبر CDN)
- **Font Awesome** - الأيقونات
- **Google Fonts** - خط Saira للعربية

## 📁 هيكل المشروع

```
Furniture_Web/
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف CSS المخصص
├── script.js           # ملف JavaScript
├── tailwind.config.js  # إعدادات Tailwind CSS
└── README.md           # ملف التوثيق
```

## ✨ المميزات

### 🎨 التصميم
- تصميم متجاوب (Responsive Design)
- دعم كامل للغة العربية (RTL)
- ألوان مخصصة لموضوع الأثاث
- تأثيرات بصرية جذابة
- خط Saira الجميل للعربية

### 🔧 الوظائف
- قائمة تنقل متجاوبة
- نموذج اتصال تفاعلي
- نظام إشعارات
- عربة تسوق بسيطة
- تأثيرات التمرير السلس
- رسوم متحركة CSS

### 📱 التوافق
- متوافق مع جميع المتصفحات الحديثة
- متجاوب مع جميع أحجام الشاشات
- محسن للأجهزة المحمولة

## 🚀 كيفية التشغيل

1. **تحميل الملفات:**
   ```bash
   git clone [repository-url]
   cd Furniture_Web
   ```

2. **فتح الموقع:**
   - افتح ملف `index.html` في المتصفح مباشرة
   - أو استخدم خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   
   # باستخدام PHP
   php -S localhost:8000
   ```

3. **عرض الموقع:**
   - افتح المتصفح واذهب إلى `http://localhost:8000`

## 🎯 الأقسام الرئيسية

### 1. الرأس (Header)
- شعار المتجر
- قائمة التنقل
- أيقونات البحث وعربة التسوق
- قائمة محمولة

### 2. القسم الرئيسي (Hero)
- عنوان جذاب
- وصف المتجر
- زر دعوة للعمل

### 3. المنتجات (Products)
- عرض فئات المنتجات
- بطاقات تفاعلية
- أزرار إضافة للسلة

### 4. من نحن (About)
- معلومات عن المتجر
- المميزات والخدمات
- تصميم جذاب

### 5. اتصل بنا (Contact)
- معلومات التواصل
- نموذج اتصال تفاعلي
- التحقق من صحة البيانات

### 6. التذييل (Footer)
- روابط سريعة
- وسائل التواصل الاجتماعي
- حقوق النشر

## 🎨 الألوان المستخدمة

```css
/* الألوان الأساسية */
--furniture-brown: #8B4513    /* البني الأساسي */
--furniture-gold: #DAA520     /* الذهبي */
--furniture-cream: #F5F5DC    /* الكريمي */
```

## 📝 التخصيص

### تغيير الألوان:
1. عدّل المتغيرات في `tailwind.config.js`
2. أو غيّر الألوان في `styles.css`

### إضافة محتوى:
1. عدّل النصوص في `index.html`
2. أضف صور المنتجات
3. حدّث معلومات التواصل

### تخصيص الوظائف:
1. عدّل `script.js` لإضافة وظائف جديدة
2. أضف تفاعلات مخصصة

## 🔧 التطوير المستقبلي

- [ ] إضافة صفحات منتجات منفصلة
- [ ] تكامل مع قاعدة بيانات
- [ ] نظام دفع إلكتروني
- [ ] لوحة تحكم إدارية
- [ ] تحسين SEO
- [ ] إضافة المزيد من الرسوم المتحركة

## 📞 الدعم

إذا كان لديك أي أسئلة أو اقتراحات، لا تتردد في التواصل!

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتجاري.

---

**تم التطوير بـ ❤️ باستخدام HTML, CSS, JavaScript & Tailwind CSS**
