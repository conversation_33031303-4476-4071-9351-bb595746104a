<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Furniture Shope</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Saira:ital,wdth,wght@0,50..125,100..900;1,50..125,100..900&display=swap"
        rel="stylesheet">

    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'saira': ['Saira', 'sans-serif'],
                    },
                    colors: {
                        'furniture-brown': '#8B4513',
                        'furniture-gold': '#DAA520',
                        'furniture-cream': '#F5F5DC',
                    }
                }
            }
        }
    </script>
</head>

<body class="font-saira bg-gray-50">

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>

</html>