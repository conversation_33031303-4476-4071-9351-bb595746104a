/* Custom CSS for Furniture Store */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Animation classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Custom hover effects */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.3s ease;
}

/* Custom button styles */
.btn-primary {
    background: linear-gradient(135deg, #8B4513, #A0522D);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #A0522D, #8B4513);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(139, 69, 19, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #DAA520, #FFD700);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #FFD700, #DAA520);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(218, 165, 32, 0.3);
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #8B4513;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #8B4513;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #A0522D;
}

/* Mobile menu animation */
.mobile-menu-enter {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card hover effects */
.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Form focus effects */
.form-input:focus {
    border-color: #8B4513;
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
    outline: none;
}

/* Responsive text */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

/* RTL specific styles */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Custom gradient backgrounds */
.gradient-bg-1 {
    background: linear-gradient(135deg, #F5F5DC 0%, #FFFFFF 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
}

/* Icon animations */
.icon-bounce:hover {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}