// Tailwind CSS Configuration for Furniture Store
module.exports = {
  content: [
    "./*.html",
    "./*.js",
    "./src/**/*.{html,js}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'saira': ['Saira', 'sans-serif'],
        'arabic': ['Saira', 'Tajawal', 'sans-serif'],
      },
      colors: {
        'furniture': {
          'brown': '#8B4513',
          'light-brown': '#A0522D',
          'dark-brown': '#654321',
          'gold': '#DAA520',
          'light-gold': '#FFD700',
          'cream': '#F5F5DC',
          'beige': '#F5F5DC',
        },
        'custom': {
          'primary': '#8B4513',
          'secondary': '#DAA520',
          'accent': '#F5F5DC',
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        }
      },
      boxShadow: {
        'furniture': '0 10px 25px -5px rgba(139, 69, 19, 0.1), 0 4px 6px -2px rgba(139, 69, 19, 0.05)',
        'furniture-lg': '0 20px 25px -5px rgba(139, 69, 19, 0.1), 0 10px 10px -5px rgba(139, 69, 19, 0.04)',
      },
      backgroundImage: {
        'furniture-gradient': 'linear-gradient(135deg, #8B4513 0%, #A0522D 100%)',
        'gold-gradient': 'linear-gradient(135deg, #DAA520 0%, #FFD700 100%)',
        'cream-gradient': 'linear-gradient(135deg, #F5F5DC 0%, #FFFFFF 100%)',
      }
    },
  },
  plugins: [],
  // Enable RTL support
  corePlugins: {
    // Enable all core plugins
  },
  // Custom utilities for RTL
  safelist: [
    'space-x-reverse',
    'divide-x-reverse',
  ]
}